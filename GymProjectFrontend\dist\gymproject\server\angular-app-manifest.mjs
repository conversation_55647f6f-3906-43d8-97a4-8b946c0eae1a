
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: '5ada5cd6512e51ecf4e859b7b50658456ce766e26bdb751a8e8f212a4dc47eed', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '5cdc1e6cfd13f2a00e27e7819c43c9887937dfb1aff00859ebc775382173a4a2', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-UJ27JA5M.css': {size: 298495, hash: 'KuJzfxHS510', text: () => import('./assets-chunks/styles-UJ27JA5M_css.mjs').then(m => m.default)}
  },
};
