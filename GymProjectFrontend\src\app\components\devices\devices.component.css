/* Mevcut oturum i<PERSON><PERSON> stil */
.current-device {
  background-color: rgba(25, 135, 84, 0.15) !important;
  border-left: 4px solid var(--bs-success) !important;
  box-shadow: 0 0 0 1px rgba(25, 135, 84, 0.2) !important;
}

.current-device:hover {
  background-color: rgba(25, 135, 84, 0.2) !important;
}

/* Dark mode için mevcut oturum stili */
[data-bs-theme="dark"] .current-device {
  background-color: rgba(25, 135, 84, 0.25) !important;
  border-left: 4px solid var(--bs-success) !important;
  box-shadow: 0 0 0 1px rgba(25, 135, 84, 0.3) !important;
}

[data-bs-theme="dark"] .current-device:hover {
  background-color: rgba(25, 135, 84, 0.35) !important;
}