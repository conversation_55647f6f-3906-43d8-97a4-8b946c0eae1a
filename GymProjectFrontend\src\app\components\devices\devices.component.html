<div class="container-fluid mt-4">
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="fade-in" [class.content-blur]="isLoading">
    <div class="row">
      <!-- Devices Stats -->
      <div class="col-md-12 mb-4">
        <div class="row">
          <div class="col-md-4">
            <div class="modern-stats-card bg-primary-light slide-in-left">
              <div class="modern-stats-icon bg-primary text-white">
                <i class="fas fa-mobile-alt"></i>
              </div>
              <div class="modern-stats-info">
                <h2 class="modern-stats-value">{{ devices.length }}</h2>
                <p class="modern-stats-label">Toplam Aktif Cihaz</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="modern-stats-card bg-info-light slide-in-left" style="animation-delay: 0.1s;">
              <div class="modern-stats-icon bg-info text-white">
                <i class="fas fa-clock"></i>
              </div>
              <div class="modern-stats-info">
                <h2 class="modern-stats-value">{{ getLastActiveDevice() }}</h2>
                <p class="modern-stats-label">Son Aktif Cihaz</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="modern-stats-card bg-warning-light slide-in-left" style="animation-delay: 0.2s;">
              <div class="modern-stats-icon bg-warning text-white">
                <i class="fas fa-map-marker-alt"></i>
              </div>
              <div class="modern-stats-info">
                <h2 class="modern-stats-value">{{ getUniqueIpCount() }}</h2>
                <p class="modern-stats-label">Farklı IP Adresi</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Devices Table -->
      <div class="col-md-12">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <div class="d-flex justify-content-between align-items-center">
              <h5><i class="fas fa-mobile-alt me-2"></i>Aktif Cihazlarım</h5>
              <div class="d-flex">
                <div class="position-relative me-2">
                  <input 
                    type="text" 
                    class="modern-form-control" 
                    placeholder="Cihaz ara..." 
                    [(ngModel)]="searchTerm" 
                    (input)="filterDevices()">
                  <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);"></i>
                </div>
                <button class="modern-btn modern-btn-danger" (click)="revokeAllDevices()">
                  <i class="fas fa-power-off modern-btn-icon"></i>Tüm Oturumları Sonlandır
                </button>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>
                      <div class="d-flex align-items-center">
                        <span>Cihaz Bilgisi</span>
                        <i class="fas fa-sort ms-1" style="cursor: pointer;" (click)="sortDevices('deviceInfo')"></i>
                      </div>
                    </th>
                    <th>
                      <div class="d-flex align-items-center">
                        <span>Son IP Adresi</span>
                        <i class="fas fa-sort ms-1" style="cursor: pointer;" (click)="sortDevices('lastIpAddress')"></i>
                      </div>
                    </th>
                    <th>
                      <div class="d-flex align-items-center">
                        <span>İlk Giriş</span>
                        <i class="fas fa-sort ms-1" style="cursor: pointer;" (click)="sortDevices('createdAt')"></i>
                      </div>
                    </th>
                    <th>
                      <div class="d-flex align-items-center">
                        <span>Son Kullanım</span>
                        <i class="fas fa-sort ms-1" style="cursor: pointer;" (click)="sortDevices('lastUsedAt')"></i>
                      </div>
                    </th>
                    <th>İşlemler</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let device of filteredDevices" class="fade-in" [class.current-device]="device.isCurrentDevice">
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="device-icon me-2">
                          <i [class]="getDeviceIcon(device.deviceInfo)"></i>
                        </div>
                        <div>
                          {{ device.deviceInfo }}
                          <span *ngIf="device.isCurrentDevice" class="modern-badge modern-badge-success ms-2">
                            <i class="fas fa-check-circle me-1"></i>Mevcut Oturum
                          </span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span class="modern-badge modern-badge-info">
                        <i class="fas fa-network-wired me-1"></i>
                        {{ device.lastIpAddress }}
                      </span>
                    </td>
                    <td>{{ device.createdAt | date:'medium' }}</td>
                    <td>{{ device.lastUsedAt | date:'medium' }}</td>
                    <td>
                      <button
                        *ngIf="!device.isCurrentDevice"
                        class="modern-btn modern-btn-danger modern-btn-sm"
                        (click)="revokeDevice(device.id)">
                        <i class="fas fa-power-off modern-btn-icon"></i>Sonlandır
                      </button>
                      <button
                        *ngIf="device.isCurrentDevice"
                        class="modern-btn modern-btn-warning modern-btn-sm"
                        (click)="revokeCurrentDevice(device.id)">
                        <i class="fas fa-sign-out-alt modern-btn-icon"></i>Çıkış Yap
                      </button>
                    </td>
                  </tr>
                  <tr *ngIf="filteredDevices.length === 0">
                    <td colspan="5" class="text-center py-4">
                      <div class="text-muted">
                        <i class="fas fa-mobile-alt fa-3x mb-3"></i>
                        <p>Aktif cihaz bulunamadı.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modern-card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <span class="modern-badge modern-badge-primary">Toplam: {{ filteredDevices.length }} cihaz</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .bg-primary-light {
    background-color: var(--primary-light);
  }
  
  .bg-info-light {
    background-color: var(--info-light);
  }
  
  .bg-warning-light {
    background-color: var(--warning-light);
  }
  
  .bg-primary {
    background-color: var(--primary);
  }
  
  .bg-info {
    background-color: var(--info);
  }
  
  .bg-warning {
    background-color: var(--warning);
  }
  
  .device-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .content-blur {
    filter: blur(3px);
    pointer-events: none;
  }
</style>
